{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "EdTech/reactapp/175.4spbtdzkua.js", "AssetFile": "EdTech/reactapp/175.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "219827"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vOcjBxkvmhLSqS+PXCfHU1D7MhoTpxRg2eOMfdUmqxQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4spbtdzkua"}, {"Name": "integrity", "Value": "sha256-vOcjBxkvmhLSqS+PXCfHU1D7MhoTpxRg2eOMfdUmqxQ="}, {"Name": "label", "Value": "EdTech/reactapp/175.js"}]}, {"Route": "EdTech/reactapp/175.js", "AssetFile": "EdTech/reactapp/175.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219827"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vOcjBxkvmhLSqS+PXCfHU1D7MhoTpxRg2eOMfdUmqxQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vOcjBxkvmhLSqS+PXCfHU1D7MhoTpxRg2eOMfdUmqxQ="}]}, {"Route": "EdTech/reactapp/175.js.map", "AssetFile": "EdTech/reactapp/175.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "562177"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb+JX0=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb+JX0="}]}, {"Route": "EdTech/reactapp/175.js.mrzq9y32w4.map", "AssetFile": "EdTech/reactapp/175.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "562177"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb+JX0=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrzq9y32w4"}, {"Name": "integrity", "Value": "sha256-bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb+JX0="}, {"Name": "label", "Value": "EdTech/reactapp/175.js.map"}]}, {"Route": "EdTech/reactapp/185.23t9yabonc.js", "AssetFile": "EdTech/reactapp/185.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3436517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "23t9yabonc"}, {"Name": "integrity", "Value": "sha256-fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8="}, {"Name": "label", "Value": "EdTech/reactapp/185.js"}]}, {"Route": "EdTech/reactapp/185.js", "AssetFile": "EdTech/reactapp/185.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3436517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8="}]}, {"Route": "EdTech/reactapp/185.js.LICENSE.95gyj95t05.txt", "AssetFile": "EdTech/reactapp/185.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34299"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "95gyj95t05"}, {"Name": "integrity", "Value": "sha256-wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg="}, {"Name": "label", "Value": "EdTech/reactapp/185.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/185.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/185.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "34299"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg="}]}, {"Route": "EdTech/reactapp/185.js.map", "AssetFile": "EdTech/reactapp/185.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11873346"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"N2Glc7c741k+36euUNplrCMbo/wwh9z74OrYNWAKXa0=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N2Glc7c741k+36euUNplrCMbo/wwh9z74OrYNWAKXa0="}]}, {"Route": "EdTech/reactapp/185.js.n8g4689brp.map", "AssetFile": "EdTech/reactapp/185.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11873346"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"N2Glc7c741k+36euUNplrCMbo/wwh9z74OrYNWAKXa0=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8g4689brp"}, {"Name": "integrity", "Value": "sha256-N2Glc7c741k+36euUNplrCMbo/wwh9z74OrYNWAKXa0="}, {"Name": "label", "Value": "EdTech/reactapp/185.js.map"}]}, {"Route": "EdTech/reactapp/525.dvrkq7h6at.js", "AssetFile": "EdTech/reactapp/525.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "423393"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"58844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dvrkq7h6at"}, {"Name": "integrity", "Value": "sha256-58844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI="}, {"Name": "label", "Value": "EdTech/reactapp/525.js"}]}, {"Route": "EdTech/reactapp/525.js", "AssetFile": "EdTech/reactapp/525.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "423393"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"58844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-58844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI="}]}, {"Route": "EdTech/reactapp/525.js.LICENSE.0f2hyzvqhq.txt", "AssetFile": "EdTech/reactapp/525.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0f2hyzvqhq"}, {"Name": "integrity", "Value": "sha256-fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34="}, {"Name": "label", "Value": "EdTech/reactapp/525.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/525.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/525.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34="}]}, {"Route": "EdTech/reactapp/525.js.map", "AssetFile": "EdTech/reactapp/525.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1824392"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI="}]}, {"Route": "EdTech/reactapp/525.js.x2mwtu8ust.map", "AssetFile": "EdTech/reactapp/525.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1824392"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x2mwtu8ust"}, {"Name": "integrity", "Value": "sha256-yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI="}, {"Name": "label", "Value": "EdTech/reactapp/525.js.map"}]}, {"Route": "EdTech/reactapp/652.js", "AssetFile": "EdTech/reactapp/652.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12571"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/NIAn4qcOl8VJTos+Jmrc85kuLbbY1jaZ0GuFAkOfgw=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/NIAn4qcOl8VJTos+Jmrc85kuLbbY1jaZ0GuFAkOfgw="}]}, {"Route": "EdTech/reactapp/652.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/652.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ="}]}, {"Route": "EdTech/reactapp/652.js.LICENSE.ynwpht6pq8.txt", "AssetFile": "EdTech/reactapp/652.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynwpht6pq8"}, {"Name": "integrity", "Value": "sha256-nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ="}, {"Name": "label", "Value": "EdTech/reactapp/652.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/652.js.map", "AssetFile": "EdTech/reactapp/652.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44904"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks="}]}, {"Route": "EdTech/reactapp/652.js.ouwzrm6kt8.map", "AssetFile": "EdTech/reactapp/652.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44904"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ouwzrm6kt8"}, {"Name": "integrity", "Value": "sha256-EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks="}, {"Name": "label", "Value": "EdTech/reactapp/652.js.map"}]}, {"Route": "EdTech/reactapp/652.o5vpl5kna0.js", "AssetFile": "EdTech/reactapp/652.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12571"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/NIAn4qcOl8VJTos+Jmrc85kuLbbY1jaZ0GuFAkOfgw=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o5vpl5kna0"}, {"Name": "integrity", "Value": "sha256-/NIAn4qcOl8VJTos+Jmrc85kuLbbY1jaZ0GuFAkOfgw="}, {"Name": "label", "Value": "EdTech/reactapp/652.js"}]}, {"Route": "EdTech/reactapp/915.5jzajkzt9s.js", "AssetFile": "EdTech/reactapp/915.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16112"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YQVZ6NrdDd99tqjxhQNTBuiJD1x+8hiW6LyHxFSLvx8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5jzajkzt9s"}, {"Name": "integrity", "Value": "sha256-YQVZ6NrdDd99tqjxhQNTBuiJD1x+8hiW6LyHxFSLvx8="}, {"Name": "label", "Value": "EdTech/reactapp/915.js"}]}, {"Route": "EdTech/reactapp/915.js", "AssetFile": "EdTech/reactapp/915.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16112"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YQVZ6NrdDd99tqjxhQNTBuiJD1x+8hiW6LyHxFSLvx8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YQVZ6NrdDd99tqjxhQNTBuiJD1x+8hiW6LyHxFSLvx8="}]}, {"Route": "EdTech/reactapp/915.js.LICENSE.ndl5lth528.txt", "AssetFile": "EdTech/reactapp/915.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "602"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"exSM61kpL+Q2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ndl5lth528"}, {"Name": "integrity", "Value": "sha256-exSM61kpL+Q2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk="}, {"Name": "label", "Value": "EdTech/reactapp/915.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/915.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/915.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "602"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"exSM61kpL+Q2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exSM61kpL+Q2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk="}]}, {"Route": "EdTech/reactapp/915.js.cltsnaqjg5.map", "AssetFile": "EdTech/reactapp/915.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55644"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cltsnaqjg5"}, {"Name": "integrity", "Value": "sha256-0MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8="}, {"Name": "label", "Value": "EdTech/reactapp/915.js.map"}]}, {"Route": "EdTech/reactapp/915.js.map", "AssetFile": "EdTech/reactapp/915.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55644"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8="}]}, {"Route": "EdTech/reactapp/BasePage.1uiseb3eij.js", "AssetFile": "EdTech/reactapp/BasePage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "828129"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1uiseb3eij"}, {"Name": "integrity", "Value": "sha256-pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg="}, {"Name": "label", "Value": "EdTech/reactapp/BasePage.js"}]}, {"Route": "EdTech/reactapp/BasePage.js", "AssetFile": "EdTech/reactapp/BasePage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "828129"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg="}]}, {"Route": "EdTech/reactapp/BasePage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/BasePage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EYQfb4zNpc3IJjKc/gkt+xODyEC5NEn3Xndt7U0s3RU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EYQfb4zNpc3IJjKc/gkt+xODyEC5NEn3Xndt7U0s3RU="}]}, {"Route": "EdTech/reactapp/BasePage.js.LICENSE.vg51q489ys.txt", "AssetFile": "EdTech/reactapp/BasePage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EYQfb4zNpc3IJjKc/gkt+xODyEC5NEn3Xndt7U0s3RU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg51q489ys"}, {"Name": "integrity", "Value": "sha256-EYQfb4zNpc3IJjKc/gkt+xODyEC5NEn3Xndt7U0s3RU="}, {"Name": "label", "Value": "EdTech/reactapp/BasePage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/BasePage.js.fni4vypgk0.map", "AssetFile": "EdTech/reactapp/BasePage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3268747"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aw7YCNC51kBn0fRh9URWv+4OLjnetHvyIjgj+4Z5P1Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fni4vypgk0"}, {"Name": "integrity", "Value": "sha256-aw7YCNC51kBn0fRh9URWv+4OLjnetHvyIjgj+4Z5P1Y="}, {"Name": "label", "Value": "EdTech/reactapp/BasePage.js.map"}]}, {"Route": "EdTech/reactapp/BasePage.js.map", "AssetFile": "EdTech/reactapp/BasePage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3268747"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aw7YCNC51kBn0fRh9URWv+4OLjnetHvyIjgj+4Z5P1Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aw7YCNC51kBn0fRh9URWv+4OLjnetHvyIjgj+4Z5P1Y="}]}, {"Route": "EdTech/reactapp/DemoLessonPage.hkmtr1gh73.js", "AssetFile": "EdTech/reactapp/DemoLessonPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7177760"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hkmtr1gh73"}, {"Name": "integrity", "Value": "sha256-fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc="}, {"Name": "label", "Value": "EdTech/reactapp/DemoLessonPage.js"}]}, {"Route": "EdTech/reactapp/DemoLessonPage.js", "AssetFile": "EdTech/reactapp/DemoLessonPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7177760"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc="}]}, {"Route": "EdTech/reactapp/DemoLessonPage.js.2an5qol5yw.map", "AssetFile": "EdTech/reactapp/DemoLessonPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23677969"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"5rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2an5qol5yw"}, {"Name": "integrity", "Value": "sha256-5rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw="}, {"Name": "label", "Value": "EdTech/reactapp/DemoLessonPage.js.map"}]}, {"Route": "EdTech/reactapp/DemoLessonPage.js.LICENSE.93q2jf4ssj.txt", "AssetFile": "EdTech/reactapp/DemoLessonPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "98850"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "93q2jf4ssj"}, {"Name": "integrity", "Value": "sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc="}, {"Name": "label", "Value": "EdTech/reactapp/DemoLessonPage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/DemoLessonPage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/DemoLessonPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "98850"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc="}]}, {"Route": "EdTech/reactapp/DemoLessonPage.js.map", "AssetFile": "EdTech/reactapp/DemoLessonPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23677969"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"5rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw="}]}, {"Route": "EdTech/reactapp/ExamManagementPage.js", "AssetFile": "EdTech/reactapp/ExamManagementPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "807079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M0kcu2X70QxK9Oz8L3jNYRsjquY+ilWOCVRve/PQY5A=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M0kcu2X70QxK9Oz8L3jNYRsjquY+ilWOCVRve/PQY5A="}]}, {"Route": "EdTech/reactapp/ExamManagementPage.js.LICENSE.9mlrlvlkrp.txt", "AssetFile": "EdTech/reactapp/ExamManagementPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2686"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9mlrlvlkrp"}, {"Name": "integrity", "Value": "sha256-MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="}, {"Name": "label", "Value": "EdTech/reactapp/ExamManagementPage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/ExamManagementPage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/ExamManagementPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2686"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="}]}, {"Route": "EdTech/reactapp/ExamManagementPage.js.j7k99epumh.map", "AssetFile": "EdTech/reactapp/ExamManagementPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2945412"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j7k99epumh"}, {"Name": "integrity", "Value": "sha256-cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo="}, {"Name": "label", "Value": "EdTech/reactapp/ExamManagementPage.js.map"}]}, {"Route": "EdTech/reactapp/ExamManagementPage.js.map", "AssetFile": "EdTech/reactapp/ExamManagementPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2945412"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo="}]}, {"Route": "EdTech/reactapp/ExamManagementPage.z8j7scqg6i.js", "AssetFile": "EdTech/reactapp/ExamManagementPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "807079"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"M0kcu2X70QxK9Oz8L3jNYRsjquY+ilWOCVRve/PQY5A=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z8j7scqg6i"}, {"Name": "integrity", "Value": "sha256-M0kcu2X70QxK9Oz8L3jNYRsjquY+ilWOCVRve/PQY5A="}, {"Name": "label", "Value": "EdTech/reactapp/ExamManagementPage.js"}]}, {"Route": "EdTech/reactapp/IconStorePage.js", "AssetFile": "EdTech/reactapp/IconStorePage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "834897"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DGzw403+FPkVC3pfRl+7j+y0znScVovOOs5nZgrfSVo=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DGzw403+FPkVC3pfRl+7j+y0znScVovOOs5nZgrfSVo="}]}, {"Route": "EdTech/reactapp/IconStorePage.js.LICENSE.7yma15gazi.txt", "AssetFile": "EdTech/reactapp/IconStorePage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2685"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"r+dgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7yma15gazi"}, {"Name": "integrity", "Value": "sha256-r+dgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M="}, {"Name": "label", "Value": "EdTech/reactapp/IconStorePage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/IconStorePage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/IconStorePage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2685"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"r+dgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r+dgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M="}]}, {"Route": "EdTech/reactapp/IconStorePage.js.gd6wyxvcha.map", "AssetFile": "EdTech/reactapp/IconStorePage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2968513"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RF4U/ZQBdYxsbCPwLu+ojPAm2YG3xLbc8vhKCWF0CNE=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gd6wyxvcha"}, {"Name": "integrity", "Value": "sha256-RF4U/ZQBdYxsbCPwLu+ojPAm2YG3xLbc8vhKCWF0CNE="}, {"Name": "label", "Value": "EdTech/reactapp/IconStorePage.js.map"}]}, {"Route": "EdTech/reactapp/IconStorePage.js.map", "AssetFile": "EdTech/reactapp/IconStorePage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2968513"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RF4U/ZQBdYxsbCPwLu+ojPAm2YG3xLbc8vhKCWF0CNE=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RF4U/ZQBdYxsbCPwLu+ojPAm2YG3xLbc8vhKCWF0CNE="}]}, {"Route": "EdTech/reactapp/IconStorePage.kqtipf7998.js", "AssetFile": "EdTech/reactapp/IconStorePage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "834897"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DGzw403+FPkVC3pfRl+7j+y0znScVovOOs5nZgrfSVo=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kqtipf7998"}, {"Name": "integrity", "Value": "sha256-DGzw403+FPkVC3pfRl+7j+y0znScVovOOs5nZgrfSVo="}, {"Name": "label", "Value": "EdTech/reactapp/IconStorePage.js"}]}, {"Route": "EdTech/reactapp/PracticeExamPage.7twizm0i3l.js", "AssetFile": "EdTech/reactapp/PracticeExamPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "807043"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7twizm0i3l"}, {"Name": "integrity", "Value": "sha256-Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8="}, {"Name": "label", "Value": "EdTech/reactapp/PracticeExamPage.js"}]}, {"Route": "EdTech/reactapp/PracticeExamPage.js", "AssetFile": "EdTech/reactapp/PracticeExamPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "807043"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8="}]}, {"Route": "EdTech/reactapp/PracticeExamPage.js.LICENSE.9mlrlvlkrp.txt", "AssetFile": "EdTech/reactapp/PracticeExamPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2686"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9mlrlvlkrp"}, {"Name": "integrity", "Value": "sha256-MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="}, {"Name": "label", "Value": "EdTech/reactapp/PracticeExamPage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/PracticeExamPage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/PracticeExamPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2686"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MQcgM+raU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="}]}, {"Route": "EdTech/reactapp/PracticeExamPage.js.map", "AssetFile": "EdTech/reactapp/PracticeExamPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2945343"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec="}]}, {"Route": "EdTech/reactapp/PracticeExamPage.js.togz1vcpq9.map", "AssetFile": "EdTech/reactapp/PracticeExamPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2945343"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "togz1vcpq9"}, {"Name": "integrity", "Value": "sha256-PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec="}, {"Name": "label", "Value": "EdTech/reactapp/PracticeExamPage.js.map"}]}, {"Route": "EdTech/reactapp/PreviewLessonPage.772vr13cpp.js", "AssetFile": "EdTech/reactapp/PreviewLessonPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7177674"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HRRG+HRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "772vr13cpp"}, {"Name": "integrity", "Value": "sha256-HRRG+HRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU="}, {"Name": "label", "Value": "EdTech/reactapp/PreviewLessonPage.js"}]}, {"Route": "EdTech/reactapp/PreviewLessonPage.js", "AssetFile": "EdTech/reactapp/PreviewLessonPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7177674"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HRRG+HRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HRRG+HRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU="}]}, {"Route": "EdTech/reactapp/PreviewLessonPage.js.LICENSE.93q2jf4ssj.txt", "AssetFile": "EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "98850"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "93q2jf4ssj"}, {"Name": "integrity", "Value": "sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc="}, {"Name": "label", "Value": "EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "98850"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ+LMIjK8VN3yPyAc="}]}, {"Route": "EdTech/reactapp/PreviewLessonPage.js.map", "AssetFile": "EdTech/reactapp/PreviewLessonPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23677790"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c="}]}, {"Route": "EdTech/reactapp/PreviewLessonPage.js.qj3zrtvq02.map", "AssetFile": "EdTech/reactapp/PreviewLessonPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23677790"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qj3zrtvq02"}, {"Name": "integrity", "Value": "sha256-tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c="}, {"Name": "label", "Value": "EdTech/reactapp/PreviewLessonPage.js.map"}]}, {"Route": "EdTech/reactapp/QuestionPage.1v77uzlqy5.js", "AssetFile": "EdTech/reactapp/QuestionPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4764115"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1v77uzlqy5"}, {"Name": "integrity", "Value": "sha256-c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg="}, {"Name": "label", "Value": "EdTech/reactapp/QuestionPage.js"}]}, {"Route": "EdTech/reactapp/QuestionPage.js", "AssetFile": "EdTech/reactapp/QuestionPage.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4764115"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg="}]}, {"Route": "EdTech/reactapp/QuestionPage.js.LICENSE.7bjzewtjcj.txt", "AssetFile": "EdTech/reactapp/QuestionPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48146"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7bjzewtjcj"}, {"Name": "integrity", "Value": "sha256-fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs="}, {"Name": "label", "Value": "EdTech/reactapp/QuestionPage.js.LICENSE.txt"}]}, {"Route": "EdTech/reactapp/QuestionPage.js.LICENSE.txt", "AssetFile": "EdTech/reactapp/QuestionPage.js.LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48146"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs="}]}, {"Route": "EdTech/reactapp/QuestionPage.js.ceshysaiu2.map", "AssetFile": "EdTech/reactapp/QuestionPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16488638"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy+n2s=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ceshysaiu2"}, {"Name": "integrity", "Value": "sha256-BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy+n2s="}, {"Name": "label", "Value": "EdTech/reactapp/QuestionPage.js.map"}]}, {"Route": "EdTech/reactapp/QuestionPage.js.map", "AssetFile": "EdTech/reactapp/QuestionPage.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16488638"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy+n2s=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy+n2s="}]}, {"Route": "EdTech/reactapp/assets/css/app.css", "AssetFile": "EdTech/reactapp/assets/css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "247592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"W+c18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6+SpStPY=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W+c18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6+SpStPY="}]}, {"Route": "EdTech/reactapp/assets/css/app.css.3m39eyus15.map", "AssetFile": "EdTech/reactapp/assets/css/app.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "289495"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dQ+RUmPNFWrSQfmdVg3x2HBismw5+9QdxLr7k7aLjgk=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3m39eyus15"}, {"Name": "integrity", "Value": "sha256-dQ+RUmPNFWrSQfmdVg3x2HBismw5+9QdxLr7k7aLjgk="}, {"Name": "label", "Value": "EdTech/reactapp/assets/css/app.css.map"}]}, {"Route": "EdTech/reactapp/assets/css/app.css.map", "AssetFile": "EdTech/reactapp/assets/css/app.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "289495"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dQ+RUmPNFWrSQfmdVg3x2HBismw5+9QdxLr7k7aLjgk=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dQ+RUmPNFWrSQfmdVg3x2HBismw5+9QdxLr7k7aLjgk="}]}, {"Route": "EdTech/reactapp/assets/css/app.dqnaqurh47.css", "AssetFile": "EdTech/reactapp/assets/css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "247592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"W+c18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6+SpStPY=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dqnaqurh47"}, {"Name": "integrity", "Value": "sha256-W+c18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6+SpStPY="}, {"Name": "label", "Value": "EdTech/reactapp/assets/css/app.css"}]}, {"Route": "EdTech/reactapp/assets/css/vendor.css", "AssetFile": "EdTech/reactapp/assets/css/vendor.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2211065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Uc17aF4+llpQWTbh9hhovepW/T+ylCwe5KiagG6YX7s=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uc17aF4+llpQWTbh9hhovepW/T+ylCwe5KiagG6YX7s="}]}, {"Route": "EdTech/reactapp/assets/css/vendor.css.0oam4xxl3a.map", "AssetFile": "EdTech/reactapp/assets/css/vendor.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2533424"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0oam4xxl3a"}, {"Name": "integrity", "Value": "sha256-gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY="}, {"Name": "label", "Value": "EdTech/reactapp/assets/css/vendor.css.map"}]}, {"Route": "EdTech/reactapp/assets/css/vendor.css.map", "AssetFile": "EdTech/reactapp/assets/css/vendor.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2533424"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY="}]}, {"Route": "EdTech/reactapp/assets/css/vendor.t1ddakriei.css", "AssetFile": "EdTech/reactapp/assets/css/vendor.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2211065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Uc17aF4+llpQWTbh9hhovepW/T+ylCwe5KiagG6YX7s=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1dd<PERSON><PERSON>i"}, {"Name": "integrity", "Value": "sha256-Uc17aF4+llpQWTbh9hhovepW/T+ylCwe5KiagG6YX7s="}, {"Name": "label", "Value": "EdTech/reactapp/assets/css/vendor.css"}]}, {"Route": "EdTech/reactapp/assets/images/CoordinateFinderGame.ig4295qi4k.jpg", "AssetFile": "EdTech/reactapp/assets/images/CoordinateFinderGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "315838"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1+DTPg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ig4295qi4k"}, {"Name": "integrity", "Value": "sha256-EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1+DTPg="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/CoordinateFinderGame.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/CoordinateFinderGame.jpg", "AssetFile": "EdTech/reactapp/assets/images/CoordinateFinderGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "315838"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1+DTPg=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1+DTPg="}]}, {"Route": "EdTech/reactapp/assets/images/Demo.fafyiyrk0h.png", "AssetFile": "EdTech/reactapp/assets/images/Demo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "769743"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GbS4fQvzM0f+PD96WCHkSqP57LvAf4mf2at7+w6pMf8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fafyiyrk0h"}, {"Name": "integrity", "Value": "sha256-GbS4fQvzM0f+PD96WCHkSqP57LvAf4mf2at7+w6pMf8="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/Demo.png"}]}, {"Route": "EdTech/reactapp/assets/images/Demo.png", "AssetFile": "EdTech/reactapp/assets/images/Demo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "769743"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GbS4fQvzM0f+PD96WCHkSqP57LvAf4mf2at7+w6pMf8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GbS4fQvzM0f+PD96WCHkSqP57LvAf4mf2at7+w6pMf8="}]}, {"Route": "EdTech/reactapp/assets/images/Game.jpg", "AssetFile": "EdTech/reactapp/assets/images/Game.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "212262"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EezFI1A4HD51SZGlPcwJpvti+6BSBT6y6a4AAW/cOhM=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EezFI1A4HD51SZGlPcwJpvti+6BSBT6y6a4AAW/cOhM="}]}, {"Route": "EdTech/reactapp/assets/images/Game.x8px27nr1p.jpg", "AssetFile": "EdTech/reactapp/assets/images/Game.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "212262"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EezFI1A4HD51SZGlPcwJpvti+6BSBT6y6a4AAW/cOhM=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x8px27nr1p"}, {"Name": "integrity", "Value": "sha256-EezFI1A4HD51SZGlPcwJpvti+6BSBT6y6a4AAW/cOhM="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/Game.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/Layout.png", "AssetFile": "EdTech/reactapp/assets/images/Layout.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "722546"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"0xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3+BDbM=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3+BDbM="}]}, {"Route": "EdTech/reactapp/assets/images/Layout.vl8hl453qh.png", "AssetFile": "EdTech/reactapp/assets/images/Layout.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "722546"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"0xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3+BDbM=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vl8hl453qh"}, {"Name": "integrity", "Value": "sha256-0xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3+BDbM="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/Layout.png"}]}, {"Route": "EdTech/reactapp/assets/images/MillionaireGame.bmremb8khj.jpg", "AssetFile": "EdTech/reactapp/assets/images/MillionaireGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "183086"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bmremb8khj"}, {"Name": "integrity", "Value": "sha256-rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/MillionaireGame.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/MillionaireGame.jpg", "AssetFile": "EdTech/reactapp/assets/images/MillionaireGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "183086"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc="}]}, {"Route": "EdTech/reactapp/assets/images/Quiz.jpg", "AssetFile": "EdTech/reactapp/assets/images/Quiz.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "202771"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA="}]}, {"Route": "EdTech/reactapp/assets/images/Quiz.yw6d7ci7jx.jpg", "AssetFile": "EdTech/reactapp/assets/images/Quiz.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "202771"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yw6d7ci7jx"}, {"Name": "integrity", "Value": "sha256-Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/Quiz.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/QuizCardGame.f2vwb9aeox.jpg", "AssetFile": "EdTech/reactapp/assets/images/QuizCardGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "214716"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f2vwb9aeox"}, {"Name": "integrity", "Value": "sha256-RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/QuizCardGame.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/QuizCardGame.jpg", "AssetFile": "EdTech/reactapp/assets/images/QuizCardGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "214716"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU="}]}, {"Route": "EdTech/reactapp/assets/images/Simulator.jpg", "AssetFile": "EdTech/reactapp/assets/images/Simulator.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "214937"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q="}]}, {"Route": "EdTech/reactapp/assets/images/Simulator.mfpa3oss8v.jpg", "AssetFile": "EdTech/reactapp/assets/images/Simulator.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "214937"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mfpa3oss8v"}, {"Name": "integrity", "Value": "sha256-QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/Simulator.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/TreasureHuntGame.jpg", "AssetFile": "EdTech/reactapp/assets/images/TreasureHuntGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "240809"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4="}]}, {"Route": "EdTech/reactapp/assets/images/TreasureHuntGame.yw2yt9l9j9.jpg", "AssetFile": "EdTech/reactapp/assets/images/TreasureHuntGame.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "240809"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yw2yt9l9j9"}, {"Name": "integrity", "Value": "sha256-viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/TreasureHuntGame.jpg"}]}, {"Route": "EdTech/reactapp/assets/images/layers-2x.ef5uc09gvw.png", "AssetFile": "EdTech/reactapp/assets/images/layers-2x.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1259"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ef5uc09gvw"}, {"Name": "integrity", "Value": "sha256-Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/layers-2x.png"}]}, {"Route": "EdTech/reactapp/assets/images/layers-2x.png", "AssetFile": "EdTech/reactapp/assets/images/layers-2x.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1259"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8="}]}, {"Route": "EdTech/reactapp/assets/images/layers.png", "AssetFile": "EdTech/reactapp/assets/images/layers.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "696"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y="}]}, {"Route": "EdTech/reactapp/assets/images/layers.ptvguihtoq.png", "AssetFile": "EdTech/reactapp/assets/images/layers.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "696"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ptvguihtoq"}, {"Name": "integrity", "Value": "sha256-Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/layers.png"}]}, {"Route": "EdTech/reactapp/assets/images/marker-icon.png", "AssetFile": "EdTech/reactapp/assets/images/marker-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1466"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc="}]}, {"Route": "EdTech/reactapp/assets/images/marker-icon.vq9wvlav5t.png", "AssetFile": "EdTech/reactapp/assets/images/marker-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1466"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vq9wvlav5t"}, {"Name": "integrity", "Value": "sha256-V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/marker-icon.png"}]}, {"Route": "EdTech/reactapp/assets/images/marker-shadow.enp07i83pd.png", "AssetFile": "EdTech/reactapp/assets/images/marker-shadow.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "618"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "enp07i83pd"}, {"Name": "integrity", "Value": "sha256-Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/marker-shadow.png"}]}, {"Route": "EdTech/reactapp/assets/images/marker-shadow.png", "AssetFile": "EdTech/reactapp/assets/images/marker-shadow.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "618"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo="}]}, {"Route": "EdTech/reactapp/assets/images/practiceBackground.lxs3uvnp0c.png", "AssetFile": "EdTech/reactapp/assets/images/practiceBackground.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "439075"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"x+Bj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lxs3uvnp0c"}, {"Name": "integrity", "Value": "sha256-x+Bj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18="}, {"Name": "label", "Value": "EdTech/reactapp/assets/images/practiceBackground.png"}]}, {"Route": "EdTech/reactapp/assets/images/practiceBackground.png", "AssetFile": "EdTech/reactapp/assets/images/practiceBackground.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "439075"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"x+Bj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-x+Bj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18="}]}, {"Route": "EdTech/reactapp/demo.ggu62m2tlg.html", "AssetFile": "EdTech/reactapp/demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "751"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ggu62m2tlg"}, {"Name": "integrity", "Value": "sha256-UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk="}, {"Name": "label", "Value": "EdTech/reactapp/demo.html"}]}, {"Route": "EdTech/reactapp/demo.html", "AssetFile": "EdTech/reactapp/demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "751"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk="}]}, {"Route": "EdTech/reactapp/exam-management-router.js", "AssetFile": "EdTech/reactapp/exam-management-router.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "77452"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA="}]}, {"Route": "EdTech/reactapp/exam-management-router.js.map", "AssetFile": "EdTech/reactapp/exam-management-router.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "256239"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu+aHE=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu+aHE="}]}, {"Route": "EdTech/reactapp/exam-management-router.js.t7j9vx0o1r.map", "AssetFile": "EdTech/reactapp/exam-management-router.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "256239"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu+aHE=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t7j9vx0o1r"}, {"Name": "integrity", "Value": "sha256-d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu+aHE="}, {"Name": "label", "Value": "EdTech/reactapp/exam-management-router.js.map"}]}, {"Route": "EdTech/reactapp/exam-management-router.kurprhoep7.js", "AssetFile": "EdTech/reactapp/exam-management-router.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "77452"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kurprhoep7"}, {"Name": "integrity", "Value": "sha256-xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA="}, {"Name": "label", "Value": "EdTech/reactapp/exam-management-router.js"}]}, {"Route": "EdTech/reactapp/exams.html", "AssetFile": "EdTech/reactapp/exams.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "685"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww="}]}, {"Route": "EdTech/reactapp/exams.j7tki1w2gr.html", "AssetFile": "EdTech/reactapp/exams.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "685"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j7tki1w2gr"}, {"Name": "integrity", "Value": "sha256-IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww="}, {"Name": "label", "Value": "EdTech/reactapp/exams.html"}]}, {"Route": "EdTech/reactapp/iconStore.html", "AssetFile": "EdTech/reactapp/iconStore.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"0Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0="}]}, {"Route": "EdTech/reactapp/iconStore.pyfgcvdtvw.html", "AssetFile": "EdTech/reactapp/iconStore.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"0Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pyfgcvdtvw"}, {"Name": "integrity", "Value": "sha256-0Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0="}, {"Name": "label", "Value": "EdTech/reactapp/iconStore.html"}]}, {"Route": "EdTech/reactapp/index.hmbk7inl62.html", "AssetFile": "EdTech/reactapp/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "675"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J+kKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmbk7inl62"}, {"Name": "integrity", "Value": "sha256-J+kKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M="}, {"Name": "label", "Value": "EdTech/reactapp/index.html"}]}, {"Route": "EdTech/reactapp/index.html", "AssetFile": "EdTech/reactapp/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "675"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J+kKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J+kKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M="}]}, {"Route": "EdTech/reactapp/practiceExams.html", "AssetFile": "EdTech/reactapp/practiceExams.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v47LinC/xGbLZmho2FuT4+ZMC/UwTFmzi0gM5HVSSRE=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v47LinC/xGbLZmho2FuT4+ZMC/UwTFmzi0gM5HVSSRE="}]}, {"Route": "EdTech/reactapp/practiceExams.tpmxo2e1f8.html", "AssetFile": "EdTech/reactapp/practiceExams.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"v47LinC/xGbLZmho2FuT4+ZMC/UwTFmzi0gM5HVSSRE=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tpmxo2e1f8"}, {"Name": "integrity", "Value": "sha256-v47LinC/xGbLZmho2FuT4+ZMC/UwTFmzi0gM5HVSSRE="}, {"Name": "label", "Value": "EdTech/reactapp/practiceExams.html"}]}, {"Route": "EdTech/reactapp/preview.html", "AssetFile": "EdTech/reactapp/preview.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY="}]}, {"Route": "EdTech/reactapp/preview.vyvfagge59.html", "AssetFile": "EdTech/reactapp/preview.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vyvfagge59"}, {"Name": "integrity", "Value": "sha256-l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY="}, {"Name": "label", "Value": "EdTech/reactapp/preview.html"}]}, {"Route": "EdTech/reactapp/question.html", "AssetFile": "EdTech/reactapp/question.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "749"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag="}]}, {"Route": "EdTech/reactapp/question.tvj36u7iep.html", "AssetFile": "EdTech/reactapp/question.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "749"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tvj36u7iep"}, {"Name": "integrity", "Value": "sha256-l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag="}, {"Name": "label", "Value": "EdTech/reactapp/question.html"}]}, {"Route": "EdTech/reactapp/runtime.e222d9b8rg.js", "AssetFile": "EdTech/reactapp/runtime.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4637"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9hehO+u/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e222d9b8rg"}, {"Name": "integrity", "Value": "sha256-9hehO+u/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ="}, {"Name": "label", "Value": "EdTech/reactapp/runtime.js"}]}, {"Route": "EdTech/reactapp/runtime.js", "AssetFile": "EdTech/reactapp/runtime.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4637"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9hehO+u/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9hehO+u/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ="}]}, {"Route": "EdTech/reactapp/runtime.js.map", "AssetFile": "EdTech/reactapp/runtime.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22869"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"BErWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON><PERSON>rWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU="}]}, {"Route": "EdTech/reactapp/runtime.js.odtp9r21ta.map", "AssetFile": "EdTech/reactapp/runtime.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22869"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"BErWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:03:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "odtp9r21ta"}, {"Name": "integrity", "Value": "sha256-<PERSON><PERSON>rWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU="}, {"Name": "label", "Value": "EdTech/reactapp/runtime.js.map"}]}, {"Route": "_content/Blazorise.DataGrid/datagrid.js", "AssetFile": "_content/Blazorise.DataGrid/datagrid.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1044"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E7BARR5A5FTuh05khWphgfYiEtoiiel/QV4OIa6Cxus=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E7BARR5A5FTuh05khWphgfYiEtoiiel/QV4OIa6Cxus="}]}, {"Route": "_content/Blazorise.Snackbar/blazorise.snackbar.css", "AssetFile": "_content/Blazorise.Snackbar/blazorise.snackbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8S8BcdXRnz8djRDnjEPdUpytwYGpwXyHvyo7Q7fm/Yo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Dec 2023 11:19:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8S8BcdXRnz8djRDnjEPdUpytwYGpwXyHvyo7Q7fm/Yo="}]}, {"Route": "_content/Blazorise.Snackbar/blazorise.snackbar.min.css", "AssetFile": "_content/Blazorise.Snackbar/blazorise.snackbar.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9843"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"aUF2vLumi/ZJNe9492mTgcymP/UYIvw1RTAnabFlJ4k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Dec 2023 11:19:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aUF2vLumi/ZJNe9492mTgcymP/UYIvw1RTAnabFlJ4k="}]}, {"Route": "_content/Blazorise/blazorise.css", "AssetFile": "_content/Blazorise/blazorise.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70435"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Q6TOhrRznPf2Ahko6sNBgGGBBPFbjd9BvXuQhpBmbLM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q6TOhrRznPf2Ahko6sNBgGGBBPFbjd9BvXuQhpBmbLM="}]}, {"Route": "_content/Blazorise/blazorise.min.css", "AssetFile": "_content/Blazorise/blazorise.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60002"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FGT9oYq2nFvTaJeQlcZU781rrIJVGlvQ2BqOWXUOyW4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FGT9oYq2nFvTaJeQlcZU781rrIJVGlvQ2BqOWXUOyW4="}]}, {"Route": "_content/Blazorise/breakpoint.js", "AssetFile": "_content/Blazorise/breakpoint.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2379"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4="}]}, {"Route": "_content/Blazorise/button.js", "AssetFile": "_content/Blazorise/button.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"w76IOZhdJXYnkEdRzFWZGRAkVq7Ngq5jrsTYXXLhitc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w76IOZhdJXYnkEdRzFWZGRAkVq7Ngq5jrsTYXXLhitc="}]}, {"Route": "_content/Blazorise/closable.js", "AssetFile": "_content/Blazorise/closable.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No="}]}, {"Route": "_content/Blazorise/colorPicker.js", "AssetFile": "_content/Blazorise/colorPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7047"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lflUJsn2eKMmdVXo713nV+g4wTr5Um0OXoWFyOKL5eg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lflUJsn2eKMmdVXo713nV+g4wTr5Um0OXoWFyOKL5eg="}]}, {"Route": "_content/Blazorise/datePicker.js", "AssetFile": "_content/Blazorise/datePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TpyjdeAMWFB2gx3ZQmh1UvuqYMD59bd4jQX7Za7Ly34=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TpyjdeAMWFB2gx3ZQmh1UvuqYMD59bd4jQX7Za7Ly34="}]}, {"Route": "_content/Blazorise/dragDrop.js", "AssetFile": "_content/Blazorise/dragDrop.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2440"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xbyTx5LybiiaQpNvEYH4QdDBJVRsHfJMamX2lx4gRfU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xbyTx5LybiiaQpNvEYH4QdDBJVRsHfJMamX2lx4gRfU="}]}, {"Route": "_content/Blazorise/dropdown.js", "AssetFile": "_content/Blazorise/dropdown.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1372"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FPJXeIoVZwd4YoS7WbU3wi6btRVmH7f/pX29HYIuXRk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FPJXeIoVZwd4YoS7WbU3wi6btRVmH7f/pX29HYIuXRk="}]}, {"Route": "_content/Blazorise/fileEdit.js", "AssetFile": "_content/Blazorise/fileEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZdftH0tDUsiO5PAdMi9qZqJ+msH5W8JxnIfufIha0zk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZdftH0tDUsiO5PAdMi9qZqJ+msH5W8JxnIfufIha0zk="}]}, {"Route": "_content/Blazorise/filePicker.js", "AssetFile": "_content/Blazorise/filePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1926"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"x/9O4AF1g+jINAiE3SMUUQ5lxU2vRFTiVGYiJ6KBSo8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-x/9O4AF1g+jINAiE3SMUUQ5lxU2vRFTiVGYiJ6KBSo8="}]}, {"Route": "_content/Blazorise/floatingUi.js", "AssetFile": "_content/Blazorise/floatingUi.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uP5pM2vUMG10HYhF/ciBw9BuohBiDGSdCsXXcRD2SBw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uP5pM2vUMG10HYhF/ciBw9BuohBiDGSdCsXXcRD2SBw="}]}, {"Route": "_content/Blazorise/inputMask.js", "AssetFile": "_content/Blazorise/inputMask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2604"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uqtuxvfY3i5E7Y86+DbVVcdP704aEvguuXpChVzfPuE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uqtuxvfY3i5E7Y86+DbVVcdP704aEvguuXpChVzfPuE="}]}, {"Route": "_content/Blazorise/io.js", "AssetFile": "_content/Blazorise/io.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY="}]}, {"Route": "_content/Blazorise/memoEdit.js", "AssetFile": "_content/Blazorise/memoEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3595"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tlJRRfieIuy8JhCKmRpcx+BIUiJjjtVTj1XHBfmSHuA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tlJRRfieIuy8JhCKmRpcx+BIUiJjjtVTj1XHBfmSHuA="}]}, {"Route": "_content/Blazorise/numericPicker.js", "AssetFile": "_content/Blazorise/numericPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jnDjsark145S3I3eKyso6jjMgwqQ092NBu4zPuVt27c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jnDjsark145S3I3eKyso6jjMgwqQ092NBu4zPuVt27c="}]}, {"Route": "_content/Blazorise/observer.js", "AssetFile": "_content/Blazorise/observer.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3682"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Dec 2023 11:19:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130="}]}, {"Route": "_content/Blazorise/table.js", "AssetFile": "_content/Blazorise/table.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7739"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fi5dKi9q435kVmpZOu0zwdRaksdaeEB4O9E7Aaw38kE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fi5dKi9q435kVmpZOu0zwdRaksdaeEB4O9E7Aaw38kE="}]}, {"Route": "_content/Blazorise/textEdit.js", "AssetFile": "_content/Blazorise/textEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1722"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WimrBIvH3Czi8qT7hw0vIsXCJUe63BWAfTyVlb8QiQA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WimrBIvH3Czi8qT7hw0vIsXCJUe63BWAfTyVlb8QiQA="}]}, {"Route": "_content/Blazorise/theme.js", "AssetFile": "_content/Blazorise/theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1487"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA="}]}, {"Route": "_content/Blazorise/timePicker.js", "AssetFile": "_content/Blazorise/timePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mR5wsDruFAETanzr4DDIzJl0sQ1h58mXTdXEhXWDP9Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mR5wsDruFAETanzr4DDIzJl0sQ1h58mXTdXEhXWDP9Q="}]}, {"Route": "_content/Blazorise/tooltip.js", "AssetFile": "_content/Blazorise/tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54020"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"i4GxKys4X60IIuTq0b55NHOMc99vtV0ISVEPutA2tQg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i4GxKys4X60IIuTq0b55NHOMc99vtV0ISVEPutA2tQg="}]}, {"Route": "_content/Blazorise/utilities.js", "AssetFile": "_content/Blazorise/utilities.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VnHr2nul6kypD4DzatWTBmOwWEBdzuR4ayr1B8Ar2PU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 16 Jan 2024 12:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VnHr2nul6kypD4DzatWTBmOwWEBdzuR4ayr1B8Ar2PU="}]}, {"Route": "_content/Blazorise/validators/DateTimeMaskValidator.js", "AssetFile": "_content/Blazorise/validators/DateTimeMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk="}]}, {"Route": "_content/Blazorise/validators/NoValidator.js", "AssetFile": "_content/Blazorise/validators/NoValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "113"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw="}]}, {"Route": "_content/Blazorise/validators/NumericMaskValidator.js", "AssetFile": "_content/Blazorise/validators/NumericMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6408"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE="}]}, {"Route": "_content/Blazorise/validators/RegExMaskValidator.js", "AssetFile": "_content/Blazorise/validators/RegExMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM="}]}, {"Route": "_content/Blazorise/vendors/Behave.js", "AssetFile": "_content/Blazorise/vendors/Behave.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE="}]}, {"Route": "_content/Blazorise/vendors/Pickr.js", "AssetFile": "_content/Blazorise/vendors/Pickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "27690"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM="}]}, {"Route": "_content/Blazorise/vendors/autoNumeric.js", "AssetFile": "_content/Blazorise/vendors/autoNumeric.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo="}]}, {"Route": "_content/Blazorise/vendors/flatpickr.js", "AssetFile": "_content/Blazorise/vendors/flatpickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "62541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88="}]}, {"Route": "_content/Blazorise/vendors/floating-ui-core.js", "AssetFile": "_content/Blazorise/vendors/floating-ui-core.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15103"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0="}]}, {"Route": "_content/Blazorise/vendors/floating-ui.js", "AssetFile": "_content/Blazorise/vendors/floating-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10628"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI="}]}, {"Route": "_content/Blazorise/vendors/inputmask.js", "AssetFile": "_content/Blazorise/vendors/inputmask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "140250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 13:45:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs="}]}, {"Route": "_content/Blazorise/vendors/jsencrypt.js", "AssetFile": "_content/Blazorise/vendors/jsencrypt.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55434"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM="}]}, {"Route": "_content/Blazorise/vendors/sha512.js", "AssetFile": "_content/Blazorise/vendors/sha512.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17899"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU="}]}, {"Route": "_content/Volo.Abp.AspNetCore.Components.Web/libs/abp/js/abp.js", "AssetFile": "_content/Volo.Abp.AspNetCore.Components.Web/libs/abp/js/abp.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6463"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3wC5psuyAjiAcWeN969UZdVowkiSeK/8wFI/m5W58hA=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Jun 2024 08:39:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wC5psuyAjiAcWeN969UZdVowkiSeK/8wFI/m5W58hA="}]}, {"Route": "_content/Volo.Abp.AspNetCore.Components.Web/libs/abp/js/lang-utils.js", "AssetFile": "_content/Volo.Abp.AspNetCore.Components.Web/libs/abp/js/lang-utils.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hxeGyyq4zpJoJeBT4renScsmtOa5/01PMwGZIlo15SQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Jun 2024 08:39:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hxeGyyq4zpJoJeBT4renScsmtOa5/01PMwGZIlo15SQ="}]}, {"Route": "_content/Volo.Abp.BlazoriseUI/volo.abp.blazoriseui.css", "AssetFile": "_content/Volo.Abp.BlazoriseUI/volo.abp.blazoriseui.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1274"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lB2nfdF/hnqc1gH/QRKk+PjDMbrKePCrlUxWZhZ8nnI=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Jun 2024 08:39:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lB2nfdF/hnqc1gH/QRKk+PjDMbrKePCrlUxWZhZ8nnI="}]}, {"Route": "client-proxies/edtect-study-proxy.3z86kgqw0j.js", "AssetFile": "client-proxies/edtect-study-proxy.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6805"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ERkF+iNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 09:17:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3z86kgqw0j"}, {"Name": "integrity", "Value": "sha256-ERkF+iNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU="}, {"Name": "label", "Value": "client-proxies/edtect-study-proxy.js"}]}, {"Route": "client-proxies/edtect-study-proxy.js", "AssetFile": "client-proxies/edtect-study-proxy.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6805"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ERkF+iNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 09:17:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ERkF+iNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU="}]}, {"Route": "css/lesson-config-custom.bhnzboqiya.css", "AssetFile": "css/lesson-config-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2671"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 09:17:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bhnzboqiya"}, {"Name": "integrity", "Value": "sha256-YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs="}, {"Name": "label", "Value": "css/lesson-config-custom.css"}]}, {"Route": "css/lesson-config-custom.css", "AssetFile": "css/lesson-config-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2671"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 09:17:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs="}]}, {"Route": "css/lesson-config.css", "AssetFile": "css/lesson-config.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1024"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LGcgCqx/2ub6z2TJpYO2m+jpiKWOLqpFR7RrECeAig4=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 09:17:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LGcgCqx/2ub6z2TJpYO2m+jpiKWOLqpFR7RrECeAig4="}]}, {"Route": "css/lesson-config.cuxmsiayrj.css", "AssetFile": "css/lesson-config.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1024"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LGcgCqx/2ub6z2TJpYO2m+jpiKWOLqpFR7RrECeAig4=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 09:17:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cuxms<PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-LGcgCqx/2ub6z2TJpYO2m+jpiKWOLqpFR7RrECeAig4="}, {"Name": "label", "Value": "css/lesson-config.css"}]}, {"Route": "images/home/<USER>", "AssetFile": "images/home/<USER>", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3001"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"48HX+6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 07:49:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f5ygd9cow5"}, {"Name": "integrity", "Value": "sha256-48HX+6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM="}, {"Name": "label", "Value": "images/home/<USER>"}]}, {"Route": "images/home/<USER>", "AssetFile": "images/home/<USER>", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "3001"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"48HX+6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 07:49:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-48HX+6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM="}]}, {"Route": "images/home/<USER>", "AssetFile": "images/home/<USER>", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "62413"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:02:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI="}]}, {"Route": "images/home/<USER>", "AssetFile": "images/home/<USER>", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "62413"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 08:02:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y2x72g0kd9"}, {"Name": "integrity", "Value": "sha256-OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI="}, {"Name": "label", "Value": "images/home/<USER>"}]}, {"Route": "js/common/lesson-init-module.js", "AssetFile": "js/common/lesson-init-module.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:43:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc="}]}, {"Route": "js/common/lesson-init-module.kgbamez07j.js", "AssetFile": "js/common/lesson-init-module.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 10:43:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgbamez07j"}, {"Name": "integrity", "Value": "sha256-hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc="}, {"Name": "label", "Value": "js/common/lesson-init-module.js"}]}]}